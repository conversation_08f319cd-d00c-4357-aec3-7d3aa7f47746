-- Create profile pictures table to store base64 images (one per user)
CREATE TABLE IF NOT EXISTS ptvuser.profile_pictures (
    user_id TEXT PRIMARY KEY REFERENCES ptvuser."user"(id) ON DELETE CASCADE,
    image_data TEXT NOT NULL, -- Base64 encoded image data
    image_type VARCHAR(50) NOT NULL DEFAULT 'image/jpeg', -- MIME type (image/jpeg, image/png, etc.)
    file_size INTEGER NOT NULL, -- Size in bytes for validation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT profile_pictures_file_size_check CHECK (file_size > 0 AND file_size <= 5242880), -- Max 5MB
    CONSTRAINT profile_pictures_image_type_check CHECK (image_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/webp'))
);

-- Create index for better performance (though not really needed since user_id is PK)
CREATE INDEX IF NOT EXISTS idx_profile_pictures_created_at ON ptvuser.profile_pictures(created_at DESC);

-- Create updated_at trigger (reuse existing function)
CREATE TRIGGER update_profile_pictures_updated_at
    BEFORE UPDATE ON ptvuser.profile_pictures
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE ptvuser.profile_pictures IS 'Stores user profile pictures as base64 encoded data (one per user)';
COMMENT ON COLUMN ptvuser.profile_pictures.user_id IS 'Primary key - reference to the user who owns this profile picture';
COMMENT ON COLUMN ptvuser.profile_pictures.image_data IS 'Base64 encoded image data';
COMMENT ON COLUMN ptvuser.profile_pictures.image_type IS 'MIME type of the image (image/jpeg, image/png, etc.)';
COMMENT ON COLUMN ptvuser.profile_pictures.file_size IS 'Size of the original file in bytes for validation';
COMMENT ON COLUMN ptvuser.profile_pictures.created_at IS 'Timestamp when the profile picture was uploaded';
COMMENT ON COLUMN ptvuser.profile_pictures.updated_at IS 'Timestamp when the profile picture was last updated';
