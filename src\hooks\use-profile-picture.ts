"use client";

import { useAuth } from "@/hooks/use-auth";
import { getImageSourceType } from "@/lib/account-schemas";
import { useCallback, useEffect, useState } from "react";

interface UseProfilePictureReturn {
  profileImage: string | undefined;
  imageSourceType: "url" | "base64" | null;
  isLoading: boolean;
  refreshProfilePicture: () => void;
}

/**
 * Custom hook to manage user profile pictures
 * Handles both base64 images from database and external URLs (Google, etc.)
 * Base64 images take priority over external URLs
 */
export function useProfilePicture(): UseProfilePictureReturn {
  const { user, isAuthenticated } = useAuth();
  const [profilePicture, setProfilePicture] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const fetchProfilePicture = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      setProfilePicture(null);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/account/data");
      const result = await response.json();

      console.log("result:", result);
      console.log(
        response.ok &&
          result.success &&
          result.data?.profile_picture?.image_data
      );

      if (
        response.ok &&
        result.success &&
        result.data?.profile_picture?.image_data
      ) {
        setProfilePicture(result.data.profile_picture.image_data);
      } else {
        setProfilePicture(null);
      }
    } catch (error) {
      console.error("Error fetching profile picture:", error);
      setProfilePicture(null);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]);

  // Fetch profile picture on mount and when user changes or when manually triggered
  useEffect(() => {
    fetchProfilePicture();
  }, [fetchProfilePicture, refreshTrigger]);

  // Create a manual refresh function that increments the trigger
  const refreshProfilePicture = useCallback(() => {
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  // Determine which image to use: custom profile picture (base64) takes priority
  const profileImage = profilePicture || user?.image || undefined;
  const imageSourceType = getImageSourceType(profileImage);

  return {
    profileImage,
    imageSourceType,
    isLoading,
    refreshProfilePicture,
  };
}
