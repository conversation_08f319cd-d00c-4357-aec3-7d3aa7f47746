import { hasuraQuery, hasuraMutation } from "./hasura";
import { UserAccountData, ProfilePicture } from "@/lib/account-schemas";

// GraphQL Queries

/**
 * Get user account data with accounts and profile picture
 */
export const GET_USER_ACCOUNT_DATA = `
  query GetUserAccountData($userId: String!) {
    ptvuser_user_by_pk(id: $userId) {
      id
      email
      username
      name
      image
      createdAt
      updatedAt
      emailVerified
      accounts {
        id
        providerId
        accountId
        createdAt
        updatedAt
      }
      profile_picture {
        user_id
        image_data
        image_type
        file_size
        created_at
        updated_at
      }
    }
  }
`;

/**
 * Get user accounts to check provider information
 */
export const GET_USER_ACCOUNTS = `
  query GetUserAccounts($userId: String!) {
    ptvuser_account(where: {userId: {_eq: $userId}}) {
      id
      providerId
      accountId
      createdAt
      updatedAt
    }
  }
`;

/**
 * Get profile picture for user
 */
export const GET_USER_PROFILE_PICTURE = `
  query GetUserProfilePicture($userId: String!) {
    ptvuser_profile_pictures_by_pk(user_id: $userId) {
      user_id
      image_data
      image_type
      file_size
      created_at
      updated_at
    }
  }
`;

// GraphQL Mutations

/**
 * Update user profile information
 */
export const UPDATE_USER_PROFILE = `
  mutation UpdateUserProfile($userId: String!, $updates: user_set_input!) {
    update_ptvuser_user_by_pk(pk_columns: {id: $userId}, _set: $updates) {
      id
      email
      username
      name
      image
      updatedAt
    }
  }
`;

/**
 * Insert or update profile picture (upsert)
 */
export const UPSERT_PROFILE_PICTURE = `
  mutation UpsertProfilePicture($object: ptvuser_profile_pictures_insert_input!) {
    insert_ptvuser_profile_pictures_one(
      object: $object,
      on_conflict: {
        constraint: profile_pictures_pkey,
        update_columns: [image_data, image_type, file_size, updated_at]
      }
    ) {
      user_id
      image_data
      image_type
      file_size
      created_at
      updated_at
    }
  }
`;

/**
 * Delete profile picture
 */
export const DELETE_PROFILE_PICTURE = `
  mutation DeleteProfilePicture($userId: String!) {
    delete_ptvuser_profile_pictures_by_pk(user_id: $userId) {
      user_id
    }
  }
`;

// Utility Functions

/**
 * Fetch complete user account data
 */
export async function getUserAccountData(
  userId: string
): Promise<UserAccountData | null> {
  try {
    const result = await hasuraQuery<{
      ptvuser_user_by_pk: UserAccountData | null;
    }>(GET_USER_ACCOUNT_DATA, { variables: { userId } });

    const userData = result.ptvuser_user_by_pk;
    if (!userData) return null;

    console.log("user dat:", userData);

    // if (userData.profile_picture) {
    //   userData.profile_picture = {
    //     user_id: userData.profile_picture.user_id,
    //     image_data: userData.profile_picture.image_data,
    //     image_type: userData.profile_picture.image_type,
    //     file_size: userData.profile_picture.file_size,
    //     created_at: userData.profile_picture.created_at,
    //     updated_at: userData.profile_picture.updated_at,
    //   };
    // }

    // delete (userData as any).profile_picture;

    return userData;
  } catch (error) {
    console.error("Error fetching user account data:", error);
    throw new Error("Nu s-au putut încărca datele contului");
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(
  userId: string,
  updates: Partial<Pick<UserAccountData, "email" | "username" | "name">>
): Promise<UserAccountData> {
  try {
    const result = await hasuraMutation<{
      update_ptvuser_user_by_pk: UserAccountData;
    }>(UPDATE_USER_PROFILE, {
      variables: {
        userId,
        updates,
      },
    });

    if (!result.update_ptvuser_user_by_pk) {
      throw new Error("Nu s-a putut actualiza profilul");
    }

    return result.update_ptvuser_user_by_pk;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw new Error("Nu s-a putut actualiza profilul");
  }
}

/**
 * Upload profile picture (upsert - insert or update)
 */
export async function uploadProfilePicture(
  userId: string,
  imageData: string,
  imageType: string,
  fileSize: number
): Promise<ProfilePicture> {
  try {
    // Use upsert to insert or update the profile picture
    const result = await hasuraMutation<{
      insert_ptvuser_profile_pictures_one: any;
    }>(UPSERT_PROFILE_PICTURE, {
      variables: {
        object: {
          user_id: userId,
          image_data: imageData,
          image_type: imageType,
          file_size: fileSize,
        },
      },
    });

    const profilePic = result.insert_ptvuser_profile_pictures_one;

    return {
      user_id: profilePic.user_id,
      image_data: profilePic.image_data,
      image_type: profilePic.image_type,
      file_size: profilePic.file_size,
      created_at: profilePic.created_at,
      updated_at: profilePic.updated_at,
    };
  } catch (error) {
    console.error("Error uploading profile picture:", error);
    throw new Error("Nu s-a putut încărca imaginea de profil");
  }
}

/**
 * Delete profile picture
 */
export async function deleteProfilePicture(userId: string): Promise<void> {
  try {
    await hasuraMutation(DELETE_PROFILE_PICTURE, {
      variables: { userId },
    });
  } catch (error) {
    console.error("Error deleting profile picture:", error);
    throw new Error("Nu s-a putut șterge imaginea de profil");
  }
}
