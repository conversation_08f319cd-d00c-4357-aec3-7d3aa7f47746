import { accountDetailsSchema } from "@/lib/account-schemas";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function PUT(request: NextRequest) {
  try {
    // Get session
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate data
    const validation = accountDetailsSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Datele introduse nu sunt valide",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { username, name } = validation.data;

    // // Update user profile in database
    // const updatedUser = await updateUserProfile(session.user.id, {
    //   email,
    //   username,
    //   name,
    // });

    // Also update the user in better-auth if needed
    try {
      await auth.api.updateUser({
        headers: await headers(),
        body: {
          username,
          name,
        },
      });
    } catch (authError) {
      console.error("Error updating user in better-auth:", authError);
      // Continue even if better-auth update fails, as we've updated the database
    }

    return NextResponse.json({
      success: true,
      message: "Profilul a fost actualizat cu succes",
      user: null,
    });
  } catch (error) {
    console.error("Error updating profile:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "A apărut o eroare la actualizarea profilului" },
      { status: 500 }
    );
  }
}
