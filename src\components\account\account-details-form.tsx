"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  AccountDetailsFormData,
  accountDetailsSchema,
  AccountCapabilities,
} from "@/lib/account-schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Save } from "lucide-react";
import { useForm } from "react-hook-form";

interface AccountDetailsFormProps {
  initialData: AccountDetailsFormData & { createdAt?: string };
  capabilities: AccountCapabilities;
  onSubmit: (data: AccountDetailsFormData) => Promise<void>;
  isLoading?: boolean;
}

export function AccountDetailsForm({
  initialData,
  capabilities,
  onSubmit,
  isLoading = false,
}: AccountDetailsFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<AccountDetailsFormData>({
    resolver: zodResolver(accountDetailsSchema),
    defaultValues: initialData,
  });

  const handleFormSubmit = async (data: AccountDetailsFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Error updating account details:", error);
    }
  };

  return (
    <form
      onSubmit={handleSubmit(handleFormSubmit)}
      className="space-y-6 flex-4"
    >
      {/* Email Field */}
      <div className="space-y-2">
        <Label htmlFor="email">Adresă de email</Label>
        <Input
          id="email"
          type="email"
          {...register("email")}
          disabled={!capabilities.canChangeEmail || isLoading || true}
          className={errors.email ? "border-red-500" : ""}
        />
        {errors.email && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {errors.email.message}
          </p>
        )}
        {!capabilities.canChangeEmail && (
          <p className="text-xs text-muted-foreground flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Email-ul nu poate fi modificat pentru conturile cu autentificare
            externă
          </p>
        )}
      </div>

      {/* Username Field */}
      <div className="space-y-2">
        <Label htmlFor="username">Username</Label>
        <Input
          id="username"
          type="text"
          {...register("username")}
          disabled={!capabilities.canChangeUsername || isLoading}
          className={errors.username ? "border-red-500" : ""}
        />
        {errors.username && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {errors.username.message}
          </p>
        )}
      </div>

      {/* Name Field */}
      <div className="space-y-2">
        <Label htmlFor="name">Nume</Label>
        <Input
          id="name"
          type="text"
          {...register("name")}
          disabled={!capabilities.canChangeName || isLoading}
          className={errors.name ? "border-red-500" : ""}
        />
        {errors.name && (
          <p className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-4 w-4" />
            {errors.name.message}
          </p>
        )}
      </div>

      {/* Account Creation Date */}
      <div className="space-y-2">
        <Label>Data creării contului</Label>
        <Input
          value={new Date(initialData.createdAt || "").toLocaleString("ro-RO")}
          disabled
          className="bg-muted"
        />
        <p className="text-xs text-muted-foreground">
          Această informație nu poate fi modificată
        </p>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!isDirty || isLoading}
          className="min-w-[120px]"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Se salvează...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              Salvează
            </div>
          )}
        </Button>
      </div>
    </form>
  );
}
