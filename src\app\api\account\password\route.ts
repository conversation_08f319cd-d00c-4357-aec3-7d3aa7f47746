import { auth } from "@/lib/auth";
import { passwordChangeSchema } from "@/lib/account-schemas";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function PUT(request: NextRequest) {
  try {
    // Get session
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate data
    const validation = passwordChangeSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: "Datele introduse nu sunt valide",
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { currentPassword, newPassword } = validation.data;

    // First verify the current password by attempting to sign in
    try {
      const signInResult = await auth.api.signInEmail({
        body: {
          email: session.user.email,
          password: currentPassword,
        },
      });

      if (!signInResult) {
        return NextResponse.json(
          { error: "Parola curentă este incorectă" },
          { status: 400 }
        );
      }
    } catch (signInError) {
      return NextResponse.json(
        { error: "Parola curentă este incorectă" },
        { status: 400 }
      );
    }

    // Update password using better-auth
    try {
      const updateResult = await auth.api.changePassword({
        headers: await headers(),
        body: {
          newPassword,
          currentPassword,
        },
      });

      if (!updateResult) {
        throw new Error("Nu s-a putut actualiza parola");
      }

      return NextResponse.json({
        success: true,
        message: "Parola a fost schimbată cu succes",
      });

    } catch (updateError) {
      console.error("Error updating password:", updateError);
      return NextResponse.json(
        { error: "Nu s-a putut actualiza parola" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Error changing password:", error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: "A apărut o eroare la schimbarea parolei" },
      { status: 500 }
    );
  }
}
