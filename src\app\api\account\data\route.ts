import { auth } from "@/lib/auth";
import { getUserAccountData } from "@/utils/db/account-queries";
import { headers } from "next/headers";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session?.user) {
      return NextResponse.json(
        { error: "Nu ești autentificat" },
        { status: 401 }
      );
    }

    const accountData = await getUserAccountData(session.user.id);

    return NextResponse.json({
      success: true,
      data: accountData,
    });
  } catch (error) {
    console.error("Error fetching account data:", error);

    if (error instanceof Error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(
      { error: "A apărut o eroare la încărcarea datelor contului" },
      { status: 500 }
    );
  }
}
